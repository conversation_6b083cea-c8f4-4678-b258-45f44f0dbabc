# SSH Tunnel Tray v2.0.0

A comprehensive Windows system tray application for managing SSH tunnels with advanced features including multiple profiles, performance monitoring, diagnostics, and secure credential storage.

## 🚀 Features

### Core Functionality
- **System Tray Integration**: Clean, unobtrusive system tray icon with visual status indicators
- **SSH Tunnel Management**: Create and manage SSH tunnels for secure database connections
- **Auto-Reconnection**: Intelligent reconnection with configurable retry logic
- **Real-time Monitoring**: Continuous health checks and performance monitoring

### 🆕 Enhanced Features (v2.0)

#### Multiple Connection Profiles
- Support for multiple SSH tunnel configurations
- Easy switching between different environments (production, staging, development)
- Profile-specific settings and credentials

#### Advanced Security
- **Secure Credential Storage**: Windows Credential Manager integration for SSH key passphrases
- **SSH Agent Support**: Use SSH agent instead of direct key files
- **Advanced SSH Options**: Compression, keep-alive settings, connection timeouts

#### Performance & Monitoring
- **Connection Statistics**: Track uptime, reconnection count, latency
- **Performance Monitoring**: Real-time latency measurements
- **Comprehensive Diagnostics**: Test SSH connectivity, port availability, key validation

#### User Experience
- **Toast Notifications**: Windows 10/11 native notifications with fallback
- **Global Hotkeys**: Keyboard shortcuts for quick tunnel control
- **Dynamic Menu**: Context menu that updates based on connection status
- **Detailed Logging**: Comprehensive logging with configurable levels

#### Maintenance & Updates
- **Auto-Update Checking**: Check GitHub releases for newer versions
- **Configuration Import/Export**: Backup and share configurations
- **Log Viewer**: Easy access to application logs
- **Diagnostics Tool**: Built-in connection troubleshooting

## 📋 System Requirements

- Windows 10/11
- Python 3.8+ (for development)
- OpenSSH client (built into Windows 10/11)

## 🔧 Installation

### Option 1: Download Executable (Recommended)
1. Download the latest release from [GitHub Releases](https://github.com/user/ssh-tunnel-tray/releases)
2. Extract and run `SSHTunnelTray.exe`

### Option 2: Run from Source
```bash
# Clone repository
git clone https://github.com/user/ssh-tunnel-tray.git
cd ssh-tunnel-tray

# Install dependencies
pip install -r requirements_new.txt

# Run application
python ssh_tunnel_tray.py
```

### Option 3: Build Executable
```bash
# Install dependencies
pip install -r requirements_new.txt

# Build executable
pyinstaller SSHTunnelTray.spec

# Find executable in dist/ folder
```

## ⚙️ Configuration

### First Run
On first launch, you'll be prompted to configure your SSH connection:
- **Profile Name**: Descriptive name for this connection
- **SSH Host**: Jump server IP/hostname
- **SSH User**: Username for SSH connection
- **PEM Key Path**: Path to your SSH private key
- **Local Port**: Local port for tunnel (default: 5566)
- **Remote Host**: Target database server
- **Remote Port**: Target database port

### Multiple Profiles
Create additional profiles through Settings → Add Profile. Switch between profiles using the tray menu.

### Advanced Settings
- **Auto-reconnect**: Automatically reconnect on connection loss
- **Auto-connect on launch**: Connect automatically when application starts
- **SSH Agent**: Use SSH agent instead of key files
- **Compression**: Enable SSH compression for better performance
- **Keep-alive settings**: Configure connection persistence
- **Logging level**: Control verbosity of logs

## 🎯 Usage

### System Tray Icon
- **Red**: Disconnected
- **Orange**: Connecting/transitioning
- **Green**: Connected and ready

### Menu Actions
- **Left-click**: Toggle connection (connect/disconnect)
- **Right-click**: Show context menu with options:
  - Connection status and statistics
  - Profile selection (if multiple profiles)
  - Connect/Disconnect
  - Settings
  - Diagnostics
  - View Logs
  - Check Updates
  - Exit

### Keyboard Shortcuts (Optional)
- **Ctrl+Shift+T**: Toggle tunnel connection
- **Ctrl+Shift+S**: Open settings dialog

### Diagnostics
Run comprehensive connection tests:
- SSH host reachability
- SSH port accessibility
- Local port availability
- SSH key validation
- SSH agent status

## 📊 Monitoring & Statistics

The application tracks:
- **Connection uptime**: Current session and total uptime
- **Reconnection attempts**: Count of automatic reconnections
- **Latency measurements**: Real-time tunnel performance
- **Connection history**: Detailed logs of all connection events

## 🔒 Security Features

### Credential Storage
- SSH key passphrases stored securely in Windows Credential Manager
- No sensitive data in configuration files
- Support for SSH agent integration

### Connection Security
- Advanced SSH options for secure connections
- Batch mode operation (non-interactive)
- Configurable connection timeouts
- Exit on forward failure protection

## 🛠️ Troubleshooting

### Common Issues

**Icon not visible in system tray**
- Check Windows notification area settings
- Look in "hidden icons" area (click up arrow in system tray)

**Connection fails**
- Run Diagnostics from the tray menu
- Check SSH key permissions and path
- Verify SSH host accessibility
- Ensure local port is not in use

**Performance issues**
- Enable SSH compression in settings
- Adjust keep-alive intervals
- Check network connectivity

### Log Files
Application logs are stored in the same directory as the executable:
- `ssh_tunnel.log`: Detailed application logs
- Access via tray menu → View Logs

### Diagnostics Tool
Use the built-in diagnostics tool to test:
- SSH host connectivity
- Port availability
- SSH key validation
- Network configuration

## 🔄 Updates

The application can automatically check for updates:
- Enable in Settings → Check for updates
- Manual check via tray menu → Check Updates
- Download from GitHub releases page

## 📝 Configuration File

Settings are stored in `config.json` with the following structure:
```json
{
  "profiles": {
    "default": {
      "name": "Production DB",
      "ssh_host": "jump.example.com",
      "ssh_user": "ubuntu",
      "pem_path": "C:/keys/production.pem",
      "local_port": 5566,
      "remote_host": "db.internal.com",
      "remote_port": 3306,
      "auto_reconnect": true,
      "compression": true
    }
  },
  "active_profile": "default",
  "check_updates": true,
  "enable_hotkeys": true,
  "log_level": "INFO"
}
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Built with [pystray](https://github.com/moses-palmer/pystray) for system tray integration
- Uses [Pillow](https://python-pillow.org/) for icon generation
- Optional dependencies for enhanced features:
  - [win10toast](https://github.com/jithurjacob/Windows-10-Toast-Notifications) for notifications
  - [keyring](https://github.com/jaraco/keyring) for secure storage
  - [keyboard](https://github.com/boppreh/keyboard) for global hotkeys
  - [requests](https://docs.python-requests.org/) for update checking
