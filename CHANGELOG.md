# Changelog

All notable changes to SSH Tunnel Tray will be documented in this file.

## [2.0.0] - 2025-01-13

### 🚀 Major Features Added

#### Multiple Connection Profiles
- **NEW**: Support for multiple SSH tunnel configurations
- **NEW**: Easy profile switching via tray menu
- **NEW**: Profile-specific settings and credentials
- **NEW**: Import/export configuration functionality

#### Enhanced Security
- **NEW**: Secure credential storage using Windows Credential Manager
- **NEW**: SSH agent integration support
- **NEW**: Advanced SSH connection options (compression, keep-alive, timeouts)
- **NEW**: Batch mode operation for non-interactive connections

#### Performance & Monitoring
- **NEW**: Connection statistics tracking (uptime, reconnections, latency)
- **NEW**: Real-time performance monitoring with latency measurements
- **NEW**: Comprehensive diagnostics tool for troubleshooting
- **NEW**: Performance metrics in status display

#### User Experience Improvements
- **NEW**: Windows 10/11 native toast notifications with fallback
- **NEW**: Global keyboard shortcuts (Ctrl+Shift+T, Ctrl+Shift+S)
- **NEW**: Dynamic context menu that updates based on connection status
- **NEW**: Enhanced status display with connection statistics
- **NEW**: Emoji-enhanced menu items for better visual clarity

#### Maintenance & Updates
- **NEW**: Automatic update checking from GitHub releases
- **NEW**: Built-in log viewer accessible from tray menu
- **NEW**: Comprehensive logging system with configurable levels
- **NEW**: Configuration backup and restore functionality

### 🔧 Technical Improvements

#### Code Architecture
- **IMPROVED**: Modular class-based architecture
- **NEW**: `ConnectionStats` class for tracking metrics
- **NEW**: `NotificationManager` class for cross-platform notifications
- **NEW**: `SecurityManager` class for credential handling
- **NEW**: `UpdateChecker` class for version management

#### Configuration System
- **BREAKING**: Configuration format updated to support profiles
- **NEW**: Backward compatibility with old configuration format
- **NEW**: Profile-based configuration management
- **NEW**: Advanced SSH options configuration

#### Error Handling & Logging
- **NEW**: Comprehensive logging system with file and console output
- **IMPROVED**: Better error messages and user feedback
- **NEW**: Diagnostic tools for connection troubleshooting
- **NEW**: Performance monitoring and alerting

### 🐛 Bug Fixes
- **FIXED**: pystray API compatibility issues with newer versions
- **FIXED**: Menu initialization order causing icon visibility issues
- **FIXED**: Process cleanup on application exit
- **IMPROVED**: SSH command construction with proper escaping
- **FIXED**: Port availability checking before connection attempts

### 📦 Dependencies
- **NEW**: Optional dependencies for enhanced features:
  - `win10toast>=0.9` for Windows toast notifications
  - `keyring>=24.0.0` for secure credential storage
  - `keyboard>=0.13.5` for global hotkeys
  - `requests>=2.31.0` for update checking
- **UPDATED**: Core dependencies remain the same for compatibility

### 🔄 Migration Guide

#### From v1.x to v2.0
1. **Configuration**: Old configuration files are automatically migrated to the new profile format
2. **Features**: All existing functionality is preserved
3. **New Features**: New features are opt-in and don't affect existing workflows
4. **Dependencies**: Install optional dependencies for enhanced features:
   ```bash
   pip install win10toast keyring keyboard requests
   ```

#### Configuration Changes
- Old flat configuration is moved to a "default" profile
- New profile-based structure allows multiple configurations
- All existing settings are preserved and enhanced

### 📋 Known Issues
- Global hotkeys may not work in some security-restricted environments
- Toast notifications require Windows 10/11 (graceful fallback provided)
- SSH agent integration requires properly configured SSH agent

### 🎯 Coming Soon (v2.1)
- **Planned**: GUI profile management interface
- **Planned**: Connection templates and quick setup
- **Planned**: Network usage monitoring
- **Planned**: Custom notification sounds
- **Planned**: Portable mode for USB deployment

---

## [1.0.0] - 2024-12-XX

### Initial Release
- Basic SSH tunnel management
- System tray integration
- Auto-reconnection functionality
- Simple configuration dialog
- Windows executable packaging

### Features
- Single SSH tunnel configuration
- Red/orange/green status indicators
- Basic error notifications
- Auto-connect on launch option
- Settings dialog for configuration

### Technical Details
- Built with pystray for system tray integration
- PyInstaller for executable packaging
- JSON-based configuration storage
- Basic logging to console
