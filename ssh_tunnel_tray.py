import json
import logging
import os
import socket
import subprocess
import sys
import threading
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, Optional, List, Tuple

# --- GUI/tray deps ---
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from PIL import Image, ImageDraw
import pystray

# --- Optional dependencies ---
try:
    import win10toast
    HAS_TOAST = True
except ImportError:
    HAS_TOAST = False

try:
    import keyring
    HAS_KEYRING = True
except ImportError:
    HAS_KEYRING = False

try:
    import keyboard
    HAS_KEYBOARD = True
except ImportError:
    HAS_KEYBOARD = False

try:
    import requests
    HAS_REQUESTS = True
except ImportError:
    HAS_REQUESTS = False

APP_NAME = "SSHTunnelTray"
APP_VERSION = "2.0.0"
GITHUB_REPO = "user/ssh-tunnel-tray"  # Update with actual repo

DEFAULT_CONFIG = {
    "profiles": {
        "default": {
            "name": "Default Connection",
            "pem_path": "",
            "local_port": 5566,
            "remote_host": "erp-prod-db-mysql-01.cdk8wggounv9.ap-south-1.rds.amazonaws.com",
            "remote_port": 3306,
            "ssh_user": "ubuntu",
            "ssh_host": "*************",
            "auto_reconnect": True,
            "auto_connect_on_launch": True,
            "use_ssh_agent": False,
            "compression": True,
            "keep_alive_interval": 60,
            "keep_alive_count_max": 3,
            "connect_timeout": 10,
            "tcp_keep_alive": True,
            "exit_on_forward_failure": True
        }
    },
    "active_profile": "default",
    "last_connected": False,
    "check_updates": True,
    "enable_hotkeys": True,
    "log_level": "INFO"
}

CHECK_INTERVAL_SEC = 5

# Resolve app dir + config path (next to .exe when frozen)
if getattr(sys, 'frozen', False):
    APP_DIR = Path(sys.executable).parent
else:
    APP_DIR = Path(__file__).parent
CONFIG_PATH = APP_DIR / "config.json"
LOG_PATH = APP_DIR / "ssh_tunnel.log"

# Try resolve Windows built-in ssh.exe
SSH_PATHS = [
    "ssh",  # rely on PATH
    str(Path(os.environ.get("SystemRoot", r"C:\Windows")) / "System32" / "OpenSSH" / "ssh.exe")
]

# Set up logging
def setup_logging(log_level: str = "INFO") -> None:
    """Set up application logging."""
    level = getattr(logging, log_level.upper(), logging.INFO)
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(LOG_PATH),
            logging.StreamHandler()
        ]
    )

logger = logging.getLogger(__name__)


class ConnectionStats:
    """Track connection statistics and performance metrics."""

    def __init__(self):
        self.connect_time: Optional[float] = None
        self.total_uptime: float = 0
        self.reconnect_count: int = 0
        self.last_disconnect_reason: Optional[str] = None
        self.latency_history: List[float] = []
        self.timeout_count: int = 0

    def start_connection(self) -> None:
        """Mark connection start time."""
        self.connect_time = time.time()
        logger.info("Connection started")

    def end_connection(self, reason: str = "Manual disconnect") -> None:
        """Mark connection end and update stats."""
        if self.connect_time:
            session_time = time.time() - self.connect_time
            self.total_uptime += session_time
            self.connect_time = None
            self.last_disconnect_reason = reason
            logger.info(f"Connection ended: {reason}, session time: {self._format_duration(session_time)}")

    def record_reconnect(self) -> None:
        """Record a reconnection attempt."""
        self.reconnect_count += 1
        logger.info(f"Reconnection attempt #{self.reconnect_count}")

    def record_latency(self, latency_ms: float) -> None:
        """Record tunnel latency measurement."""
        self.latency_history.append(latency_ms)
        if len(self.latency_history) > 100:  # Keep last 100 measurements
            self.latency_history.pop(0)

    def record_timeout(self) -> None:
        """Record a timeout event."""
        self.timeout_count += 1

    def get_status_text(self) -> str:
        """Get human-readable status text."""
        if self.connect_time:
            uptime = time.time() - self.connect_time
            avg_latency = sum(self.latency_history[-10:]) / len(self.latency_history[-10:]) if self.latency_history else 0
            return f"Connected for {self._format_duration(uptime)} | Latency: {avg_latency:.1f}ms"
        return f"Disconnected | Total uptime: {self._format_duration(self.total_uptime)}"

    def get_average_latency(self) -> float:
        """Get average latency from recent measurements."""
        if not self.latency_history:
            return 0.0
        return sum(self.latency_history[-10:]) / len(self.latency_history[-10:])

    def _format_duration(self, seconds: float) -> str:
        """Format duration in human-readable format."""
        if seconds < 60:
            return f"{int(seconds)}s"
        elif seconds < 3600:
            return f"{int(seconds // 60)}m {int(seconds % 60)}s"
        else:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            return f"{hours}h {minutes}m"


class NotificationManager:
    """Handle system notifications with fallback options."""

    def __init__(self):
        self.toaster = None
        if HAS_TOAST:
            try:
                self.toaster = win10toast.ToastNotifier()
            except Exception as e:
                logger.warning(f"Failed to initialize toast notifications: {e}")

    def notify(self, title: str, message: str, duration: int = 3) -> None:
        """Send notification with fallback to console."""
        logger.info(f"[{title}] {message}")

        if self.toaster:
            try:
                self.toaster.show_toast(title, message, duration=duration, threaded=True)
                return
            except Exception as e:
                logger.warning(f"Toast notification failed: {e}")

        # Fallback: print to console
        print(f"[{title}] {message}")


class SecurityManager:
    """Handle secure credential storage and SSH operations."""

    @staticmethod
    def store_passphrase(profile_name: str, passphrase: str) -> bool:
        """Store SSH key passphrase securely."""
        if not HAS_KEYRING:
            return False

        try:
            keyring.set_password(APP_NAME, f"{profile_name}_passphrase", passphrase)
            return True
        except Exception as e:
            logger.error(f"Failed to store passphrase: {e}")
            return False

    @staticmethod
    def get_passphrase(profile_name: str) -> Optional[str]:
        """Retrieve stored SSH key passphrase."""
        if not HAS_KEYRING:
            return None

        try:
            return keyring.get_password(APP_NAME, f"{profile_name}_passphrase")
        except Exception as e:
            logger.error(f"Failed to retrieve passphrase: {e}")
            return None

    @staticmethod
    def check_ssh_agent() -> bool:
        """Check if SSH agent is running and has keys."""
        try:
            result = subprocess.run(["ssh-add", "-l"], capture_output=True, timeout=5)
            return result.returncode == 0
        except Exception:
            return False


class UpdateChecker:
    """Check for application updates."""

    @staticmethod
    def check_for_updates() -> Optional[Dict[str, Any]]:
        """Check GitHub releases for newer version."""
        if not HAS_REQUESTS:
            return None

        try:
            url = f"https://api.github.com/repos/{GITHUB_REPO}/releases/latest"
            response = requests.get(url, timeout=10)
            response.raise_for_status()

            release_data = response.json()
            latest_version = release_data["tag_name"].lstrip("v")
            current_version = APP_VERSION.lstrip("v")

            if latest_version != current_version:
                return {
                    "version": latest_version,
                    "url": release_data["html_url"],
                    "notes": release_data.get("body", "")
                }
        except Exception as e:
            logger.error(f"Update check failed: {e}")

        return None


def create_circle_icon(color_rgb: tuple[int, int, int], size: int = 64):
    """Generate a simple circular icon image."""
    img = Image.new("RGBA", (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    margin = 6
    draw.ellipse([margin, margin, size - margin, size - margin], fill=color_rgb)
    # small white inner dot for a bit of depth
    inner = size // 6
    draw.ellipse([size - margin - inner, margin, size - margin, margin + inner], fill=(255, 255, 255, 180))
    return img


ICON_RED = create_circle_icon((220, 60, 60))
ICON_ORANGE = create_circle_icon((255, 145, 0))
ICON_GREEN = create_circle_icon((40, 180, 85))


class TunnelManager:
    def __init__(self, icon: Any) -> None:
        self.icon = icon
        self.proc: Optional[subprocess.Popen[bytes]] = None
        self.lock = threading.Lock()
        self.stopping = False
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)

        # Initialize new components
        self.stats = ConnectionStats()
        self.notifier = NotificationManager()
        self.security = SecurityManager()
        self.current_profile = "default"

        self.config = self._load_config()
        setup_logging(self.config.get("log_level", "INFO"))

        # Don't set status here - will be set after menu is configured
        logger.info(f"{APP_NAME} v{APP_VERSION} starting...")

        self.monitor_thread.start()

        # Set up hotkeys if enabled
        if self.config.get("enable_hotkeys", True):
            self._setup_hotkeys()

        # Check for updates if enabled
        if self.config.get("check_updates", True):
            threading.Thread(target=self._check_updates_async, daemon=True).start()

        # Optionally auto-connect on launch if last state was connected
        profile_config = self._get_active_profile()
        if profile_config.get("auto_connect_on_launch", True) and self.config.get("last_connected", False):
            threading.Thread(target=self.start_tunnel, daemon=True).start()

    # ------------- Profile Management -------------
    def _get_active_profile(self) -> Dict[str, Any]:
        """Get the currently active profile configuration."""
        profiles = self.config.get("profiles", {})
        active_profile = self.config.get("active_profile", "default")
        return profiles.get(active_profile, profiles.get("default", {}))

    def _set_active_profile(self, profile_name: str) -> None:
        """Set the active profile."""
        if profile_name in self.config.get("profiles", {}):
            self.current_profile = profile_name
            self.config["active_profile"] = profile_name
            self._save_config(self.config)
            logger.info(f"Switched to profile: {profile_name}")

    def get_profile_names(self) -> List[str]:
        """Get list of available profile names."""
        return list(self.config.get("profiles", {}).keys())

    # ------------- New Feature Methods -------------
    def _setup_hotkeys(self) -> None:
        """Set up global hotkeys if available."""
        if not HAS_KEYBOARD:
            return

        try:
            keyboard.add_hotkey('ctrl+shift+t', self.toggle_tunnel)
            keyboard.add_hotkey('ctrl+shift+s', self.open_settings)
            logger.info("Global hotkeys enabled: Ctrl+Shift+T (toggle), Ctrl+Shift+S (settings)")
        except Exception as e:
            logger.warning(f"Failed to set up hotkeys: {e}")

    def _check_updates_async(self) -> None:
        """Check for updates in background."""
        try:
            update_info = UpdateChecker.check_for_updates()
            if update_info:
                self.notifier.notify(
                    "Update Available",
                    f"Version {update_info['version']} is available. Click to download.",
                    duration=10
                )
                logger.info(f"Update available: {update_info['version']}")
        except Exception as e:
            logger.error(f"Update check failed: {e}")

    def toggle_tunnel(self) -> None:
        """Toggle tunnel state (for hotkeys and menu)."""
        if self.is_tunnel_running():
            self.stop_tunnel()
        else:
            self.start_tunnel()

    # ------------- Diagnostics -------------
    def run_diagnostics(self) -> Dict[str, Any]:
        """Run comprehensive connection diagnostics."""
        profile = self._get_active_profile()
        results = {}

        # Test SSH host reachability
        results["ssh_host_reachable"] = self._ping_host(profile.get("ssh_host", ""))

        # Test SSH port
        results["ssh_port_open"] = self._test_port(profile.get("ssh_host", ""), 22)

        # Test local port availability
        local_port = profile.get("local_port", 5566)
        results["local_port_available"] = not self._is_port_in_use(local_port)

        # Test SSH key
        results["ssh_key_valid"] = self._validate_ssh_key()

        # Test SSH agent
        results["ssh_agent_available"] = self.security.check_ssh_agent()

        logger.info(f"Diagnostics completed: {results}")
        return results

    def _ping_host(self, host: str) -> bool:
        """Test if host is reachable."""
        if not host:
            return False
        try:
            result = subprocess.run(["ping", "-n", "1", host],
                                  capture_output=True, timeout=5)
            return result.returncode == 0
        except Exception:
            return False

    def _test_port(self, host: str, port: int) -> bool:
        """Test if port is open on host."""
        if not host:
            return False
        try:
            with socket.create_connection((host, port), timeout=3):
                return True
        except Exception:
            return False

    def _is_port_in_use(self, port: int) -> bool:
        """Check if local port is already in use."""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('127.0.0.1', port))
                return False
        except OSError:
            return True

    def _validate_ssh_key(self) -> bool:
        """Validate SSH key file."""
        profile = self._get_active_profile()
        pem_path = profile.get("pem_path", "")
        if not pem_path or not Path(pem_path).exists():
            return False

        try:
            # Try to load the key (basic validation)
            with open(pem_path, 'r') as f:
                content = f.read()
                return "BEGIN" in content and "PRIVATE KEY" in content
        except Exception:
            return False

    def _monitor_tunnel_performance(self) -> None:
        """Monitor tunnel latency and performance."""
        if not self.is_tunnel_running():
            return

        profile = self._get_active_profile()
        local_port = profile.get("local_port", 5566)

        start_time = time.time()
        try:
            with socket.create_connection(("127.0.0.1", local_port), timeout=1):
                latency = (time.time() - start_time) * 1000
                self.stats.record_latency(latency)
        except Exception:
            self.stats.record_timeout()

    # ------------- config -------------
    def _load_config(self) -> Dict[str, Any]:
        if CONFIG_PATH.exists():
            try:
                with open(CONFIG_PATH, "r", encoding="utf-8") as f:
                    data = json.load(f)
                # merge with defaults to tolerate upgrades
                cfg = DEFAULT_CONFIG.copy()
                cfg.update(data)
                return cfg
            except Exception:
                pass
        # first run → open settings dialog
        cfg = self._open_settings_dialog(DEFAULT_CONFIG.copy(), first_run=True)
        self._save_config(cfg)
        return cfg

    def _save_config(self, cfg: Dict[str, Any]) -> None:
        try:
            with open(CONFIG_PATH, "w", encoding="utf-8") as f:
                json.dump(cfg, f, indent=2)
        except Exception as e:
            self._notify("Could not save config", str(e))

    # ------------- tray/status -------------
    def _set_status(self, state: str):
        # state: "stopped", "connecting", "connected"
        if state == "connected":
            self.icon.icon = ICON_GREEN
            self.icon.title = f"{APP_NAME} - Connected"
            self.icon.visible = True
            if hasattr(self.icon, 'menu') and self.icon.menu:
                self.icon.update_menu()
        elif state == "connecting":
            self.icon.icon = ICON_ORANGE
            self.icon.title = f"{APP_NAME} - Connecting…"
            self.icon.visible = True
            if hasattr(self.icon, 'menu') and self.icon.menu:
                self.icon.update_menu()
        else:
            self.icon.icon = ICON_RED
            self.icon.title = f"{APP_NAME} - Stopped"
            self.icon.visible = True
            if hasattr(self.icon, 'menu') and self.icon.menu:
                self.icon.update_menu()

    def _notify(self, title, text):
        # pystray has no native toast; use tooltip title changes
        # Optionally, you can add win10toast if you want real toasts.
        print(f"[{title}] {text}")

    # ------------- tunnel ops -------------
    def _ssh_path(self):
        for p in SSH_PATHS:
            try:
                # If it's a path, ensure it exists; if it's "ssh", just return it
                if p == "ssh":
                    return p
                if Path(p).exists():
                    return p
            except Exception:
                continue
        return "ssh"

    def _build_cmd(self) -> list[str]:
        profile = self._get_active_profile()
        pem = str(profile.get("pem_path", ""))
        lport = str(profile.get("local_port", 5566))
        rhost = str(profile.get("remote_host", ""))
        rport = str(profile.get("remote_port", 3306))
        user = str(profile.get("ssh_user", ""))
        host = str(profile.get("ssh_host", ""))

        # Check if using SSH agent
        if profile.get("use_ssh_agent", False) and self.security.check_ssh_agent():
            logger.info("Using SSH agent for authentication")
            cmd = [self._ssh_path(), "-N"]
        else:
            if not pem or not Path(pem).exists():
                raise RuntimeError("PEM key path is missing or invalid.")
            cmd = [self._ssh_path(), "-i", pem, "-N"]

        # Add port forwarding
        cmd.extend(["-L", f"{lport}:{rhost}:{rport}"])

        # Add user@host
        cmd.append(f"{user}@{host}")

        # Add advanced SSH options
        keep_alive = profile.get("keep_alive_interval", 60)
        keep_alive_max = profile.get("keep_alive_count_max", 3)
        connect_timeout = profile.get("connect_timeout", 10)

        cmd.extend([
            "-o", f"ServerAliveInterval={keep_alive}",
            "-o", f"ServerAliveCountMax={keep_alive_max}",
            "-o", f"ConnectTimeout={connect_timeout}"
        ])

        if profile.get("compression", True):
            cmd.append("-C")

        if profile.get("tcp_keep_alive", True):
            cmd.extend(["-o", "TCPKeepAlive=yes"])

        if profile.get("exit_on_forward_failure", True):
            cmd.extend(["-o", "ExitOnForwardFailure=yes"])

        # Add batch mode for non-interactive operation
        cmd.extend(["-o", "BatchMode=yes"])

        logger.debug(f"SSH command: {' '.join(cmd)}")
        return cmd

    def start_tunnel(self, _=None):
        with self.lock:
            if self.proc and self.proc.poll() is None:
                self.notifier.notify("Tunnel", "Already running.")
                self._set_status("connected" if self._is_port_open() else "connecting")
                return

            # Run diagnostics before connecting
            try:
                diagnostics = self.run_diagnostics()
                if not diagnostics.get("ssh_host_reachable", True):
                    self.notifier.notify("Connection Failed", "SSH host is not reachable")
                    return

                profile = self._get_active_profile()
                if not diagnostics.get("local_port_available", True):
                    port = profile.get("local_port", 5566)
                    self.notifier.notify("Port Conflict", f"Local port {port} is already in use")
                    return
            except Exception as e:
                logger.warning(f"Diagnostics failed: {e}")

            # try to start
            self._set_status("connecting")
            try:
                cmd = self._build_cmd()

                # hide window on Windows
                startupinfo = None
                creationflags = 0
                if os.name == "nt":
                    startupinfo = subprocess.STARTUPINFO()
                    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                    creationflags = 0x08000000  # CREATE_NO_WINDOW

                self.proc = subprocess.Popen(
                    cmd,
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL,
                    stdin=subprocess.DEVNULL,
                    startupinfo=startupinfo,
                    creationflags=creationflags
                )

                # Give it a moment to bind
                for _ in range(20):
                    time.sleep(0.25)
                    if self._is_port_open():
                        break

                if self._is_port_open():
                    self._set_status("connected")
                    self.config["last_connected"] = True
                    self._save_config(self.config)
                    self.stats.start_connection()

                    profile = self._get_active_profile()
                    profile_name = profile.get("name", "Default")
                    local_port = profile.get("local_port", 5566)
                    remote_host = profile.get("remote_host", "")
                    remote_port = profile.get("remote_port", 3306)

                    self.notifier.notify("Tunnel Connected",
                                       f"{profile_name}: localhost:{local_port} → {remote_host}:{remote_port}")
                    logger.info(f"Tunnel connected for profile: {profile_name}")
                else:
                    # process may be running but not bound; treat as connecting — monitor loop will sort it out
                    self._set_status("connecting")

            except Exception as e:
                self._set_status("stopped")
                error_msg = str(e)
                self.notifier.notify("Failed to start tunnel", error_msg)
                logger.error(f"Failed to start tunnel: {error_msg}")
                messagebox.showerror(APP_NAME, f"Failed to start tunnel:\n{error_msg}")

    def stop_tunnel(self, _=None):
        with self.lock:
            self.stopping = True
            try:
                if self.proc and self.proc.poll() is None:
                    self.proc.terminate()
                    # give it a second
                    try:
                        self.proc.wait(timeout=2)
                    except subprocess.TimeoutExpired:
                        self.proc.kill()
                        logger.warning("SSH process killed forcefully")
                # fallback: kill stray ssh processes bound to our port?
            finally:
                self.proc = None
                self.stopping = False
                self._set_status("stopped")
                self.config["last_connected"] = False
                self._save_config(self.config)
                self.stats.end_connection("Manual disconnect")
                self.notifier.notify("Tunnel Stopped", "SSH tunnel terminated.")
                logger.info("Tunnel stopped manually")

    def _is_port_open(self) -> bool:
        host = "127.0.0.1"
        profile = self._get_active_profile()
        port = int(profile.get("local_port", 5566))
        try:
            with socket.create_connection((host, port), timeout=0.5):
                return True
        except OSError:
            return False

    def _proc_alive(self) -> bool:
        return self.proc is not None and self.proc.poll() is None

    def is_tunnel_running(self) -> bool:
        """Public method to check if tunnel is running."""
        return self._proc_alive()

    def _monitor_loop(self):
        # Periodically check health and auto-reconnect if needed
        performance_counter = 0
        while True:
            try:
                time.sleep(CHECK_INTERVAL_SEC)
                with self.lock:
                    alive = self._proc_alive()
                    openp = self._is_port_open()

                    if alive and openp:
                        self._set_status("connected")

                        # Monitor performance every 30 seconds (6 * 5 second intervals)
                        performance_counter += 1
                        if performance_counter >= 6:
                            self._monitor_tunnel_performance()
                            performance_counter = 0

                    elif alive and not openp:
                        # process alive but port not listening -> transient; show connecting
                        self._set_status("connecting")
                    else:
                        # process dead
                        profile = self._get_active_profile()
                        if (profile.get("auto_reconnect", True) and
                            self.config.get("last_connected", False) and
                            not self.stopping):

                            self._set_status("connecting")
                            self.stats.record_reconnect()
                            self.stats.end_connection("Connection lost")

                            # try reconnect (non-blocking)
                            threading.Thread(target=self.start_tunnel, daemon=True).start()
                        else:
                            self._set_status("stopped")
                            if self.stats.connect_time:
                                self.stats.end_connection("Connection lost")

            except Exception as e:
                error_msg = str(e)
                self.notifier.notify("Monitor error", error_msg)
                logger.error(f"Monitor loop error: {error_msg}")

    # ------------- settings dialog -------------
    def open_settings(self, _=None):
        cfg = self._open_settings_dialog(self.config.copy(), first_run=False)
        if cfg:
            self.config = cfg
            self._save_config(self.config)
            # If currently running, restart with new settings
            if self._proc_alive():
                self.stop_tunnel()
                time.sleep(0.5)
                self.start_tunnel()

    def _open_settings_dialog(self, cfg, first_run=False):
        root = tk.Tk()
        root.title(f"{APP_NAME} Settings")
        root.geometry("520x360")
        root.resizable(False, False)

        # Center window
        root.update_idletasks()
        x = (root.winfo_screenwidth() // 2) - 260
        y = (root.winfo_screenheight() // 2) - 180
        root.geometry(f"+{x}+{y}")

        # fields
        var_pem = tk.StringVar(value=cfg.get("pem_path", ""))
        var_lport = tk.StringVar(value=str(cfg.get("local_port", 5566)))
        var_rhost = tk.StringVar(value=cfg.get("remote_host", ""))
        var_rport = tk.StringVar(value=str(cfg.get("remote_port", 3306)))
        var_user = tk.StringVar(value=cfg.get("ssh_user", ""))
        var_shost = tk.StringVar(value=cfg.get("ssh_host", ""))
        var_auto = tk.BooleanVar(value=cfg.get("auto_reconnect", True))
        var_autoconn = tk.BooleanVar(value=cfg.get("auto_connect_on_launch", True))

        pad = {'padx': 10, 'pady': 6}

        def row(r, label, widget):
            tk.Label(root, text=label, anchor="w", width=18).grid(row=r, column=0, sticky="w", **pad)
            widget.grid(row=r, column=1, sticky="we", **pad)

        e_pem = tk.Entry(root, textvariable=var_pem, width=42)
        def browse_pem():
            p = filedialog.askopenfilename(title="Select PEM file", filetypes=[("PEM files", "*.pem"), ("All files", "*.*")])
            if p:
                var_pem.set(p)
        b_pem = tk.Button(root, text="Browse…", command=browse_pem)
        tk.Label(root, text="PEM Private Key", anchor="w", width=18).grid(row=0, column=0, sticky="w", **pad)
        e_pem.grid(row=0, column=1, sticky="we", **pad)
        b_pem.grid(row=0, column=2, sticky="w", **pad)

        e_lport = tk.Entry(root, textvariable=var_lport)
        row(1, "Local Port", e_lport)

        e_rhost = tk.Entry(root, textvariable=var_rhost)
        row(2, "Remote DB Host", e_rhost)

        e_rport = tk.Entry(root, textvariable=var_rport)
        row(3, "Remote DB Port", e_rport)

        e_user = tk.Entry(root, textvariable=var_user)
        row(4, "SSH Username", e_user)

        e_shost = tk.Entry(root, textvariable=var_shost)
        row(5, "SSH Host/IP", e_shost)

        cb_auto = tk.Checkbutton(root, text="Auto‑reconnect if tunnel drops", variable=var_auto)
        cb_auto.grid(row=6, column=1, sticky="w", **pad)

        cb_autoconn = tk.Checkbutton(root, text="Auto‑connect on app launch if last state was connected", variable=var_autoconn)
        cb_autoconn.grid(row=7, column=1, sticky="w", **pad)

        btn_frame = tk.Frame(root)
        btn_frame.grid(row=8, column=0, columnspan=3, pady=16)

        result = {}

        def on_save():
            try:
                pem = var_pem.get().strip()
                if not pem:
                    raise ValueError("PEM file is required.")
                if not Path(pem).exists():
                    raise ValueError("PEM file does not exist.")
                lport = int(var_lport.get().strip())
                rhost = var_rhost.get().strip()
                rport = int(var_rport.get().strip())
                user = var_user.get().strip()
                shost = var_shost.get().strip()
                if not rhost or not user or not shost:
                    raise ValueError("Remote host, SSH user, and SSH host are required.")

                result.update({
                    "pem_path": pem,
                    "local_port": lport,
                    "remote_host": rhost,
                    "remote_port": rport,
                    "ssh_user": user,
                    "ssh_host": shost,
                    "auto_reconnect": bool(var_auto.get()),
                    "auto_connect_on_launch": bool(var_autoconn.get()),
                    "last_connected": False if first_run else self.config.get("last_connected", False)
                })
                root.destroy()
            except Exception as e:
                messagebox.showerror(APP_NAME, f"Invalid settings:\n{e}")

        def on_cancel():
            if first_run:
                # force settings on first run
                if messagebox.askokcancel(APP_NAME, "Settings are required on first run. Exit app?"):
                    root.destroy()
                    os._exit(0)
            else:
                root.destroy()

        tk.Button(btn_frame, text="Save", width=14, command=on_save).pack(side="left", padx=6)
        tk.Button(btn_frame, text="Cancel", width=14, command=on_cancel).pack(side="left", padx=6)

        root.mainloop()
        return result if result else None


def main():
    # build tray icon
    icon = pystray.Icon(APP_NAME, ICON_RED, f"{APP_NAME} - Stopped")

    # Create manager first
    manager = TunnelManager(icon)

    # Menu actions
    def start_action(icon: Any, item: Any) -> None:
        manager.start_tunnel()
    def stop_action(icon: Any, item: Any) -> None:
        manager.stop_tunnel()
    def settings_action(icon: Any, item: Any) -> None:
        manager.open_settings()
    def diagnostics_action(icon: Any, item: Any) -> None:
        results = manager.run_diagnostics()
        status_lines = []
        for key, value in results.items():
            status = "✅" if value else "❌"
            readable_key = key.replace("_", " ").title()
            status_lines.append(f"{status} {readable_key}")

        manager.notifier.notify("Diagnostics Results", "\n".join(status_lines), duration=10)

    def view_logs_action(icon: Any, item: Any) -> None:
        try:
            if LOG_PATH.exists():
                os.startfile(str(LOG_PATH))  # Windows
            else:
                manager.notifier.notify("No Logs", "Log file not found")
        except Exception as e:
            manager.notifier.notify("Error", f"Could not open logs: {e}")

    def check_updates_action(icon: Any, item: Any) -> None:
        def check_async():
            update_info = UpdateChecker.check_for_updates()
            if update_info:
                manager.notifier.notify(
                    "Update Available",
                    f"Version {update_info['version']} is available",
                    duration=10
                )
            else:
                manager.notifier.notify("No Updates", "You have the latest version")

        threading.Thread(target=check_async, daemon=True).start()

    def exit_action(icon: Any, item: Any) -> None:
        try:
            manager.stop_tunnel()
        except Exception:
            pass
        icon.stop()

    # Dynamic menu creation
    def create_menu():
        items = []

        # Status and toggle
        if manager.is_tunnel_running():
            status_text = f"✅ {manager.stats.get_status_text()}"
            items.append(pystray.MenuItem(status_text, None, enabled=False))
            items.append(pystray.MenuItem("🔌 Disconnect", stop_action, default=True))
        else:
            items.append(pystray.MenuItem("❌ Disconnected", None, enabled=False))
            items.append(pystray.MenuItem("🔌 Connect", start_action, default=True))

        items.append(pystray.Menu.SEPARATOR)

        # Profile selection (if multiple profiles exist)
        profiles = manager.get_profile_names()
        if len(profiles) > 1:
            profile_items = []
            for profile_name in profiles:
                is_active = profile_name == manager.current_profile
                check_mark = "✓ " if is_active else ""
                profile_items.append(
                    pystray.MenuItem(f"{check_mark}{profile_name}",
                                   lambda i, p, name=profile_name: manager._set_active_profile(name))
                )
            items.append(pystray.MenuItem("📋 Profiles", pystray.Menu(*profile_items)))
            items.append(pystray.Menu.SEPARATOR)

        # Tools and settings
        items.extend([
            pystray.MenuItem("⚙️ Settings", settings_action),
            pystray.MenuItem("🔍 Diagnostics", diagnostics_action),
            pystray.MenuItem("📋 View Logs", view_logs_action),
        ])

        if HAS_REQUESTS:
            items.append(pystray.MenuItem("🔄 Check Updates", check_updates_action))

        items.append(pystray.Menu.SEPARATOR)
        items.append(pystray.MenuItem("❌ Exit", exit_action))

        return pystray.Menu(*items)

    # Set up dynamic menu
    icon.menu = create_menu()

    # Update menu periodically
    def update_menu():
        while True:
            time.sleep(10)  # Update every 10 seconds
            try:
                icon.menu = create_menu()
                icon.update_menu()
            except Exception as e:
                logger.error(f"Menu update error: {e}")

    threading.Thread(target=update_menu, daemon=True).start()

    # Update the status now that menu is set
    manager._set_status("stopped")

    icon.visible = True
    icon.run()


if __name__ == "__main__":
    main()
