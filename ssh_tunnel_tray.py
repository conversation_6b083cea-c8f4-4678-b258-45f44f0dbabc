import json
import os
import socket
import subprocess
import sys
import threading
import time
from pathlib import Path
from typing import Any, Dict, Optional

# --- GUI/tray deps ---
import tkinter as tk
from tkinter import filedialog, messagebox
from PIL import Image, ImageDraw
import pystray

APP_NAME = "SSHTunnelTray"
DEFAULT_CONFIG = {
    "pem_path": "",
    "local_port": 5566,
    "remote_host": "erp-prod-db-mysql-01.cdk8wggounv9.ap-south-1.rds.amazonaws.com",
    "remote_port": 3306,
    "ssh_user": "ubuntu",
    "ssh_host": "*************",
    "auto_reconnect": True,
    "auto_connect_on_launch": True,
    "last_connected": False
}

CHECK_INTERVAL_SEC = 5

# Resolve app dir + config path (next to .exe when frozen)
if getattr(sys, 'frozen', False):
    APP_DIR = Path(sys.executable).parent
else:
    APP_DIR = Path(__file__).parent
CONFIG_PATH = APP_DIR / "config.json"

# Try resolve Windows built-in ssh.exe
SSH_PATHS = [
    "ssh",  # rely on PATH
    str(Path(os.environ.get("SystemRoot", r"C:\Windows")) / "System32" / "OpenSSH" / "ssh.exe")
]


def create_circle_icon(color_rgb: tuple[int, int, int], size: int = 64):
    """Generate a simple circular icon image."""
    img = Image.new("RGBA", (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    margin = 6
    draw.ellipse([margin, margin, size - margin, size - margin], fill=color_rgb)
    # small white inner dot for a bit of depth
    inner = size // 6
    draw.ellipse([size - margin - inner, margin, size - margin, margin + inner], fill=(255, 255, 255, 180))
    return img


ICON_RED = create_circle_icon((220, 60, 60))
ICON_ORANGE = create_circle_icon((255, 145, 0))
ICON_GREEN = create_circle_icon((40, 180, 85))


class TunnelManager:
    def __init__(self, icon: Any) -> None:
        self.icon = icon
        self.proc: Optional[subprocess.Popen[bytes]] = None
        self.lock = threading.Lock()
        self.stopping = False
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)

        self.config = self._load_config()
        self._set_status("stopped")

        self.monitor_thread.start()

        # Optionally auto-connect on launch if last state was connected
        if self.config.get("auto_connect_on_launch", True) and self.config.get("last_connected", False):
            threading.Thread(target=self.start_tunnel, daemon=True).start()

    # ------------- config -------------
    def _load_config(self) -> Dict[str, Any]:
        if CONFIG_PATH.exists():
            try:
                with open(CONFIG_PATH, "r", encoding="utf-8") as f:
                    data = json.load(f)
                # merge with defaults to tolerate upgrades
                cfg = DEFAULT_CONFIG.copy()
                cfg.update(data)
                return cfg
            except Exception:
                pass
        # first run → open settings dialog
        cfg = self._open_settings_dialog(DEFAULT_CONFIG.copy(), first_run=True)
        self._save_config(cfg)
        return cfg

    def _save_config(self, cfg: Dict[str, Any]) -> None:
        try:
            with open(CONFIG_PATH, "w", encoding="utf-8") as f:
                json.dump(cfg, f, indent=2)
        except Exception as e:
            self._notify("Could not save config", str(e))

    # ------------- tray/status -------------
    def _set_status(self, state: str):
        # state: "stopped", "connecting", "connected"
        if state == "connected":
            self.icon.icon = ICON_GREEN
            self.icon.title = f"{APP_NAME} - Connected"
            self.icon.visible = True
            self.icon.update_menu()
        elif state == "connecting":
            self.icon.icon = ICON_ORANGE
            self.icon.title = f"{APP_NAME} - Connecting…"
            self.icon.visible = True
            self.icon.update_menu()
        else:
            self.icon.icon = ICON_RED
            self.icon.title = f"{APP_NAME} - Stopped"
            self.icon.visible = True
            self.icon.update_menu()

    def _notify(self, title, text):
        # pystray has no native toast; use tooltip title changes
        # Optionally, you can add win10toast if you want real toasts.
        print(f"[{title}] {text}")

    # ------------- tunnel ops -------------
    def _ssh_path(self):
        for p in SSH_PATHS:
            try:
                # If it's a path, ensure it exists; if it's "ssh", just return it
                if p == "ssh":
                    return p
                if Path(p).exists():
                    return p
            except Exception:
                continue
        return "ssh"

    def _build_cmd(self) -> list[str]:
        pem = str(self.config["pem_path"])
        lport = str(self.config["local_port"])
        rhost = str(self.config["remote_host"])
        rport = str(self.config["remote_port"])
        user = str(self.config["ssh_user"])
        host = str(self.config["ssh_host"])

        if not pem or not Path(pem).exists():
            raise RuntimeError("PEM key path is missing or invalid.")

        # ssh -i "C:\ppk\erp-pro-app.pem" -N -L 5566:host:3306 ubuntu@************* -o keepalives...
        cmd = [
            self._ssh_path(),
            "-i", pem,
            "-N",
            "-L", f"{lport}:{rhost}:{rport}",
            f"{user}@{host}",
            "-o", "ServerAliveInterval=60",
            "-o", "ServerAliveCountMax=3"
        ]
        return cmd

    def start_tunnel(self, _=None):
        with self.lock:
            if self.proc and self.proc.poll() is None:
                self._notify("Tunnel", "Already running.")
                self._set_status("connected" if self._is_port_open() else "connecting")
                return

            # try to start
            self._set_status("connecting")
            try:
                cmd = self._build_cmd()

                # hide window on Windows
                startupinfo = None
                creationflags = 0
                if os.name == "nt":
                    startupinfo = subprocess.STARTUPINFO()
                    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                    creationflags = 0x08000000  # CREATE_NO_WINDOW

                self.proc = subprocess.Popen(
                    cmd,
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL,
                    stdin=subprocess.DEVNULL,
                    startupinfo=startupinfo,
                    creationflags=creationflags
                )
                # Give it a moment to bind
                for _ in range(20):
                    time.sleep(0.25)
                    if self._is_port_open():
                        break

                if self._is_port_open():
                    self._set_status("connected")
                    self.config["last_connected"] = True
                    self._save_config(self.config)
                    self._notify("Tunnel Connected", f"localhost:{self.config['local_port']} → {self.config['remote_host']}:{self.config['remote_port']}")
                else:
                    # process may be running but not bound; treat as connecting — monitor loop will sort it out
                    self._set_status("connecting")

            except Exception as e:
                self._set_status("stopped")
                self._notify("Failed to start tunnel", str(e))
                messagebox.showerror(APP_NAME, f"Failed to start tunnel:\n{e}")

    def stop_tunnel(self, _=None):
        with self.lock:
            self.stopping = True
            try:
                if self.proc and self.proc.poll() is None:
                    self.proc.terminate()
                    # give it a second
                    try:
                        self.proc.wait(timeout=2)
                    except subprocess.TimeoutExpired:
                        self.proc.kill()
                # fallback: kill stray ssh processes bound to our port?
            finally:
                self.proc = None
                self.stopping = False
                self._set_status("stopped")
                self.config["last_connected"] = False
                self._save_config(self.config)
                self._notify("Tunnel Stopped", "SSH tunnel terminated.")

    def _is_port_open(self) -> bool:
        host = "127.0.0.1"
        port = int(self.config["local_port"])
        try:
            with socket.create_connection((host, port), timeout=0.5):
                return True
        except OSError:
            return False

    def _proc_alive(self) -> bool:
        return self.proc is not None and self.proc.poll() is None

    def is_tunnel_running(self) -> bool:
        """Public method to check if tunnel is running."""
        return self._proc_alive()

    def _monitor_loop(self):
        # Periodically check health and auto-reconnect if needed
        while True:
            try:
                time.sleep(CHECK_INTERVAL_SEC)
                with self.lock:
                    alive = self._proc_alive()
                    openp = self._is_port_open()

                    if alive and openp:
                        self._set_status("connected")
                    elif alive and not openp:
                        # process alive but port not listening -> transient; show connecting
                        self._set_status("connecting")
                    else:
                        # process dead
                        if self.config.get("auto_reconnect", True) and self.config.get("last_connected", False) and not self.stopping:
                            self._set_status("connecting")
                            # try reconnect (non-blocking)
                            threading.Thread(target=self.start_tunnel, daemon=True).start()
                        else:
                            self._set_status("stopped")
            except Exception as e:
                self._notify("Monitor error", str(e))

    # ------------- settings dialog -------------
    def open_settings(self, _=None):
        cfg = self._open_settings_dialog(self.config.copy(), first_run=False)
        if cfg:
            self.config = cfg
            self._save_config(self.config)
            # If currently running, restart with new settings
            if self._proc_alive():
                self.stop_tunnel()
                time.sleep(0.5)
                self.start_tunnel()

    def _open_settings_dialog(self, cfg, first_run=False):
        root = tk.Tk()
        root.title(f"{APP_NAME} Settings")
        root.geometry("520x360")
        root.resizable(False, False)

        # Center window
        root.update_idletasks()
        x = (root.winfo_screenwidth() // 2) - 260
        y = (root.winfo_screenheight() // 2) - 180
        root.geometry(f"+{x}+{y}")

        # fields
        var_pem = tk.StringVar(value=cfg.get("pem_path", ""))
        var_lport = tk.StringVar(value=str(cfg.get("local_port", 5566)))
        var_rhost = tk.StringVar(value=cfg.get("remote_host", ""))
        var_rport = tk.StringVar(value=str(cfg.get("remote_port", 3306)))
        var_user = tk.StringVar(value=cfg.get("ssh_user", ""))
        var_shost = tk.StringVar(value=cfg.get("ssh_host", ""))
        var_auto = tk.BooleanVar(value=cfg.get("auto_reconnect", True))
        var_autoconn = tk.BooleanVar(value=cfg.get("auto_connect_on_launch", True))

        pad = {'padx': 10, 'pady': 6}

        def row(r, label, widget):
            tk.Label(root, text=label, anchor="w", width=18).grid(row=r, column=0, sticky="w", **pad)
            widget.grid(row=r, column=1, sticky="we", **pad)

        e_pem = tk.Entry(root, textvariable=var_pem, width=42)
        def browse_pem():
            p = filedialog.askopenfilename(title="Select PEM file", filetypes=[("PEM files", "*.pem"), ("All files", "*.*")])
            if p:
                var_pem.set(p)
        b_pem = tk.Button(root, text="Browse…", command=browse_pem)
        tk.Label(root, text="PEM Private Key", anchor="w", width=18).grid(row=0, column=0, sticky="w", **pad)
        e_pem.grid(row=0, column=1, sticky="we", **pad)
        b_pem.grid(row=0, column=2, sticky="w", **pad)

        e_lport = tk.Entry(root, textvariable=var_lport)
        row(1, "Local Port", e_lport)

        e_rhost = tk.Entry(root, textvariable=var_rhost)
        row(2, "Remote DB Host", e_rhost)

        e_rport = tk.Entry(root, textvariable=var_rport)
        row(3, "Remote DB Port", e_rport)

        e_user = tk.Entry(root, textvariable=var_user)
        row(4, "SSH Username", e_user)

        e_shost = tk.Entry(root, textvariable=var_shost)
        row(5, "SSH Host/IP", e_shost)

        cb_auto = tk.Checkbutton(root, text="Auto‑reconnect if tunnel drops", variable=var_auto)
        cb_auto.grid(row=6, column=1, sticky="w", **pad)

        cb_autoconn = tk.Checkbutton(root, text="Auto‑connect on app launch if last state was connected", variable=var_autoconn)
        cb_autoconn.grid(row=7, column=1, sticky="w", **pad)

        btn_frame = tk.Frame(root)
        btn_frame.grid(row=8, column=0, columnspan=3, pady=16)

        result = {}

        def on_save():
            try:
                pem = var_pem.get().strip()
                if not pem:
                    raise ValueError("PEM file is required.")
                if not Path(pem).exists():
                    raise ValueError("PEM file does not exist.")
                lport = int(var_lport.get().strip())
                rhost = var_rhost.get().strip()
                rport = int(var_rport.get().strip())
                user = var_user.get().strip()
                shost = var_shost.get().strip()
                if not rhost or not user or not shost:
                    raise ValueError("Remote host, SSH user, and SSH host are required.")

                result.update({
                    "pem_path": pem,
                    "local_port": lport,
                    "remote_host": rhost,
                    "remote_port": rport,
                    "ssh_user": user,
                    "ssh_host": shost,
                    "auto_reconnect": bool(var_auto.get()),
                    "auto_connect_on_launch": bool(var_autoconn.get()),
                    "last_connected": False if first_run else self.config.get("last_connected", False)
                })
                root.destroy()
            except Exception as e:
                messagebox.showerror(APP_NAME, f"Invalid settings:\n{e}")

        def on_cancel():
            if first_run:
                # force settings on first run
                if messagebox.askokcancel(APP_NAME, "Settings are required on first run. Exit app?"):
                    root.destroy()
                    os._exit(0)
            else:
                root.destroy()

        tk.Button(btn_frame, text="Save", width=14, command=on_save).pack(side="left", padx=6)
        tk.Button(btn_frame, text="Cancel", width=14, command=on_cancel).pack(side="left", padx=6)

        root.mainloop()
        return result if result else None


def main():
    # build tray icon
    icon = pystray.Icon(APP_NAME, ICON_RED, f"{APP_NAME} - Stopped")

    manager = TunnelManager(icon)

    # Menu actions
    def start_action(icon: Any, item: Any) -> None:
        manager.start_tunnel()
    def stop_action(icon: Any, item: Any) -> None:
        manager.stop_tunnel()
    def settings_action(icon: Any, item: Any) -> None:
        manager.open_settings()
    def exit_action(icon: Any, item: Any) -> None:
        try:
            manager.stop_tunnel()
        except Exception:
            pass
        icon.stop()

    # Double‑click toggles start/stop
    def toggle_action(icon: Any, item: Any) -> None:
        # Left button pressed -> toggle
        if manager.is_tunnel_running():
            manager.stop_tunnel()
        else:
            manager.start_tunnel()

    icon.menu = pystray.Menu(
        pystray.MenuItem("Toggle Tunnel", toggle_action, default=True),
        pystray.MenuItem("Start Tunnel", start_action),
        pystray.MenuItem("Stop Tunnel", stop_action),
        pystray.MenuItem("Change Settings", settings_action),
        pystray.MenuItem("Exit", exit_action)
    )

    icon.visible = True
    icon.run()


if __name__ == "__main__":
    main()
