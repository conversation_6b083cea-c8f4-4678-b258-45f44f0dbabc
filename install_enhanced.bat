@echo off
echo Installing SSH Tunnel Tray Enhanced Features...
echo.

echo Installing core dependencies...
pip install pystray==0.19.5 Pillow==10.4.0 pyinstaller==6.11.1

echo.
echo Installing enhanced features (optional)...
echo.

echo [1/4] Installing toast notifications...
pip install win10toast>=0.9

echo [2/4] Installing secure credential storage...
pip install keyring>=24.0.0

echo [3/4] Installing global hotkeys...
pip install keyboard>=0.13.5

echo [4/4] Installing update checking...
pip install requests>=2.31.0

echo.
echo Installation complete!
echo.
echo You can now run the application with enhanced features:
echo   python ssh_tunnel_tray.py
echo.
echo Or build the executable:
echo   pyinstaller SSHTunnelTray.spec
echo.
pause
